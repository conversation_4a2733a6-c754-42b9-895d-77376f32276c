{"version": 3, "sources": ["../../../../@quasar/app-vite/exports/wrappers/wrappers.js"], "sourcesContent": ["// Functions in this file are no-op,\n//  they just take a callback function and return it\n// They're used to apply typings to the callback\n//  parameters and return value when using Quasar with TypeScript\n\nconst wrapper = callback => callback\n\nexport const defineConfig = wrapper\n\nexport const defineBoot = wrapper\nexport const definePreFetch = wrapper\nexport const defineRouter = wrapper\nexport const defineStore = wrapper\n\nexport const defineSsrMiddleware = wrapper\nexport const defineSsrCreate = wrapper\nexport const defineSsrInjectDevMiddleware = wrapper\nexport const defineSsrListen = wrapper\nexport const defineSsrClose = wrapper\nexport const defineSsrServeStaticContent = wrapper\nexport const defineSsrRenderPreloadTag = wrapper\n"], "mappings": ";;;AAKA,IAAM,UAAU,cAAY;AAErB,IAAM,eAAe;AAErB,IAAM,aAAa;AACnB,IAAM,iBAAiB;AACvB,IAAM,eAAe;AACrB,IAAM,cAAc;AAEpB,IAAM,sBAAsB;AAC5B,IAAM,kBAAkB;AACxB,IAAM,+BAA+B;AACrC,IAAM,kBAAkB;AACxB,IAAM,iBAAiB;AACvB,IAAM,8BAA8B;AACpC,IAAM,4BAA4B;", "names": []}