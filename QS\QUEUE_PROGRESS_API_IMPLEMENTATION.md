# 掛號進度 API 實作總結

## 概述

本文件說明掛號進度 API 的完整實作，包括前端和後端的實現。

## 後端實作

### 1. API 端點

**端點:** `GET /api/v1/branches/{id}/schedules`

**參數:**
- `id` (path): 分店 ID

**回應格式:**
```json
{
  "success": true,
  "message": "取得班次資訊成功",
  "data": {
    "branch_id": 1,
    "branch_name": "北門診所",
    "schedules": [
      {
        "id": 1,
        "doctor_name": "王醫師",
        "shift": "早班",
        "current_number": 15,
        "waiting_count": 3
      },
      {
        "id": 2,
        "doctor_name": "李醫師",
        "shift": "午班",
        "current_number": 8,
        "waiting_count": 5
      }
    ]
  }
}
```

### 2. 實作檔案

#### 2.1 Handler (`internal/api/branch_handler.go`)
- 新增 `GetBranchSchedules` 方法
- 處理路徑參數驗證
- 調用服務層獲取資料

#### 2.2 Service (`internal/service/branch_service.go`)
- 新增 `GetBranchSchedules` 介面方法
- 實作業務邏輯（目前使用模擬資料）
- **TODO**: 需要實作從 timing 資料庫獲取實際資料

#### 2.3 路由註冊
- 在 `RegisterRoutes` 中新增 `/:id/schedules` 路由

### 3. TODO 項目

```go
// TODO: 實作從 timing 資料庫獲取班次資訊的邏輯
// 這裡需要根據分店的 timing 資料庫連線資訊來獲取實際的班次資料
// 暫時返回模擬資料
```

需要實作：
1. 根據分店的 timing 資料庫連線資訊建立連線
2. 查詢實際的醫師班次資料
3. 查詢當前看診號碼和等待人數
4. 將資料轉換為 DTO 格式

## 前端實作

### 1. Composable (`composables/useSchedules.ts`)

提供掛號進度相關的響應式狀態和方法：

```typescript
const { 
  schedules,           // 班次資料
  loading,            // 載入狀態
  error,              // 錯誤訊息
  fetchBranchSchedules, // 獲取班次方法
  reset               // 重置狀態
} = useSchedules();
```

### 2. 頁面整合 (`pages/IndexPage.vue`)

#### 2.1 自動載入
- 當用戶選擇分店時，自動調用 API 獲取班次資訊

#### 2.2 手動重新整理
- 點擊重新整理按鈕時，重新獲取當前分店的班次資訊
- 包含 30 秒冷卻時間限制

#### 2.3 UI 顯示
- **載入狀態**: 顯示載入動畫
- **錯誤狀態**: 顯示錯誤訊息
- **班次資訊**: 以卡片形式顯示每個醫師的班次
- **無資料狀態**: 顯示友好的提示訊息

### 3. 班次卡片設計

每個班次卡片包含：
- 醫師姓名
- 班別（早班/午班/晚班）
- 目前看診號碼
- 等待人數
- hover 效果和響應式設計

## API 測試

### 測試腳本更新

`test_api.sh` 已更新，包含：
1. 健康檢查
2. 取得所有分店
3. **新增**: 取得分店班次資訊
4. 測試不存在的分店
5. 測試無效的分店 ID

### 測試命令

```bash
# 後端測試
cd backend
chmod +x test_api.sh
./test_api.sh

# 手動測試
curl http://localhost:8082/api/v1/branches/1/schedules
```

## 使用流程

1. **用戶選擇分店**: 自動載入該分店的班次資訊
2. **顯示班次**: 以卡片形式展示各醫師的看診進度
3. **手動重新整理**: 點擊重新整理按鈕更新資料
4. **冷卻限制**: 30 秒內無法重複重新整理

## 錯誤處理

- **網路錯誤**: 顯示網路錯誤訊息
- **API 錯誤**: 顯示後端返回的錯誤訊息
- **無效分店**: 顯示分店不存在的錯誤
- **載入狀態**: 提供視覺反饋

## 下一步開發

1. **資料庫整合**: 實作 timing 資料庫的實際查詢邏輯
2. **即時更新**: 考慮使用 WebSocket 或 SSE 實現即時更新
3. **快取機制**: 添加適當的快取策略
4. **錯誤重試**: 實作自動重試機制
5. **單元測試**: 添加前後端的單元測試

## 注意事項

1. 目前後端使用模擬資料，需要等待資料庫結構確認後實作實際查詢
2. 前端已完全實作，可以正常顯示從後端獲取的資料
3. API 路由已註冊，可以通過測試腳本驗證功能
4. 冷卻時間機制有效防止頻繁請求
