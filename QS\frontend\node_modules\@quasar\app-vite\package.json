{"name": "@quasar/app-vite", "version": "2.3.0", "description": "Quasar Framework App CLI with Vite", "bin": {"quasar": "./bin/quasar.js"}, "type": "module", "files": ["exports", "assets", "bin", "lib", "templates", "types"], "types": "./types/index.d.ts", "exports": {".": {"types": "./types/index.d.ts"}, "./package.json": "./package.json", "./bin/quasar": {"node": "./bin/quasar.js"}, "./wrappers": {"types": "./types/app-wrappers.d.ts", "require": "./exports/wrappers/wrappers.cjs", "node": "./exports/wrappers/wrappers.js", "import": "./exports/wrappers/wrappers.js"}, "./bex/background": {"types": "./types/bex/entrypoints/background.d.ts", "import": "./exports/bex/background.js"}, "./bex/content": {"types": "./types/bex/entrypoints/content.d.ts", "import": "./exports/bex/content.js"}, "./bex/private/bex-bridge": {"types": "./types/bex/bex-bridge.d.ts", "import": "./exports/bex/private/bex-bridge.js"}, "./eslint": {"require": "./exports/eslint/eslint.cjs", "node": "./exports/eslint/eslint.js", "import": "./exports/eslint/eslint.js"}, "./lib/testing.js": {"import": "./exports/testing/testing.js"}}, "keywords": ["quasar", "vue", "v<PERSON><PERSON><PERSON>", "vite", "cli", "web", "spa", "pwa", "electron", "<PERSON><PERSON>"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "razvan.sto<PERSON><PERSON><PERSON>@gmail.com", "url": "https://github.com/quasarframework"}, "repository": {"type": "git", "url": "https://github.com/quasarframework/quasar"}, "license": "MIT", "bugs": "https://github.com/quasarframework/quasar/issues", "homepage": "https://quasar.dev", "funding": {"type": "github", "url": "https://donate.quasar.dev"}, "dependencies": {"@types/chrome": "^0.0.262", "@types/compression": "^1.7.5", "@types/cordova": "^11.0.3", "@types/express": "^4.17.21", "@vitejs/plugin-vue": "^5.1.4", "archiver": "^7.0.1", "chokidar": "^3.6.0", "ci-info": "^4.0.0", "compression": "^1.7.5", "confbox": "^0.1.8", "cross-spawn": "^7.0.6", "dot-prop": "9.0.0", "dotenv": "^16.4.5", "dotenv-expand": "^11.0.6", "elementtree": "0.1.7", "esbuild": "^0.25.0", "express": "^4.21.2", "fs-extra": "^11.2.0", "html-minifier-terser": "^7.2.0", "inquirer": "^9.3.7", "isbinaryfile": "^5.0.4", "kolorist": "^1.8.0", "lodash": "^4.17.21", "minimist": "^1.2.8", "mlly": "^1.7.4", "open": "^10.1.0", "rollup-plugin-visualizer": "^5.13.1", "sass-embedded": "^1.83.0", "semver": "^7.6.3", "serialize-javascript": "^6.0.2", "tinyglobby": "^0.2.10", "ts-essentials": "^9.4.2", "vite": "^6.1.0", "webpack-merge": "^6.0.1", "@quasar/vite-plugin": "^1.10.0", "@quasar/ssl-certificate": "^1.0.0", "@quasar/render-ssr-error": "^1.0.3"}, "devDependencies": {"@electron/packager": "^18.3.5", "electron-builder": "^24.13.3", "eslint": "^9.29.0", "pinia": "^3.0.1", "typescript": "^5.6.3", "vue": "^3.5.12", "vue-router": "^4.4.5", "workbox-build": "^7.3.0", "quasar": "^2.18.1", "eslint-config-quasar": "0.0.3"}, "peerDependencies": {"@electron/packager": ">= 18", "electron-builder": ">= 22", "pinia": "^2.0.0 || ^3.0.0", "quasar": "^2.16.0", "typescript": ">= 5.4", "vue": "^3.2.29", "vue-router": "^4.0.12", "workbox-build": ">= 6"}, "peerDependenciesMeta": {"typescript": {"optional": true}, "electron-builder": {"optional": true}, "@electron/packager": {"optional": true}, "eslint": {"optional": true}, "pinia": {"optional": true}, "workbox-build": {"optional": true}}, "engines": {"node": "^30 || ^28 || ^26 || ^24 || ^22 || ^20 || ^18", "npm": ">= 6.14.12", "yarn": ">= 1.17.3"}, "volta": {"node": "lts"}, "publishConfig": {"access": "public"}, "scripts": {"lint": "eslint --cache --fix"}}