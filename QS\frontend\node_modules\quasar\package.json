{"name": "quasar", "version": "2.18.2", "description": "Build high-performance VueJS user interfaces (SPA, PWA, SSR, Mobile and Desktop) in record time", "type": "module", "module": "dist/quasar.client.js", "unpkg": "dist/quasar.umd.prod.js", "jsdelivr": "dist/quasar.umd.prod.js", "typings": "dist/types/index.d.ts", "files": ["dist", "lang", "icon-set", "src", "wrappers"], "exports": {".": {"types": "./dist/types/index.d.ts", "require": "./dist/quasar.server.prod.cjs", "node": "./dist/quasar.server.prod.js", "import": "./dist/quasar.client.js"}, "./wrappers": {"require": "./wrappers/index.cjs", "import": "./wrappers/index.js"}, "./*": "./*"}, "repository": {"type": "git", "url": "git+https://github.com/quasarframework/quasar.git"}, "keywords": ["v<PERSON><PERSON><PERSON>", "vue", "quasar", "js", "phone", "tablet", "desktop", "spa", "pwa", "website", "electron"], "author": {"name": "<PERSON><PERSON><PERSON>", "email": "razvan.sto<PERSON><PERSON><PERSON>@gmail.com", "url": "https://quasar.dev"}, "license": "MIT", "bugs": {"url": "https://github.com/quasarframework/quasar/issues"}, "funding": {"type": "github", "url": "https://donate.quasar.dev"}, "homepage": "https://quasar.dev", "devDependencies": {"autoprefixer": "^10.4.20", "cli-highlight": "^2.1.11", "cross-env": "^7.0.3", "cssnano": "^7.0.6", "diff": "^5.2.0", "esbuild": "^0.25.0", "eslint": "^9.29.0", "fs-extra": "^11.2.0", "kolorist": "^1.8.0", "open": "^10.1.0", "postcss-rtlcss": "^5.4.0", "prettier": "^3.3.3", "sass-embedded": "^1.80.2", "table": "^6.8.2", "tinyglobby": "^0.2.10", "typescript": "^5.6.2", "vue": "^3.5.5", "@quasar/extras": "1.17.0", "eslint-config-quasar": "0.0.3"}, "vetur": {"tags": "dist/vetur/quasar-tags.json", "attributes": "dist/vetur/quasar-attributes.json"}, "web-types": "dist/web-types/web-types.json", "browserslist": ["last 10 Chrome versions", "last 10 Firefox versions", "last 10 Edge versions", "last 10 Safari versions", "last 10 Android versions", "last 10 ChromeAndroid versions", "last 10 FirefoxAndroid versions", "last 10 iOS versions"], "engines": {"node": ">= 10.18.1", "npm": ">= 6.13.4", "yarn": ">= 1.21.1"}, "scripts": {"clean": "node build/script.clean.js", "build": "node build/script.build.js", "dev": "cd ./playground && quasar dev && cd ..", "dev:ssr": "cd ./playground && quasar dev -m ssr && cd ..", "dev:build": "cd ./playground && quasar build && cd ..", "dev:build:ssr": "cd ./playground && quasar build -m ssr && cd ..", "dev:umd": "node build/script.test-umd.js", "dev:quploader": "cd ./playground/upload-server && pnpm i && cd ../.. && node playground/upload-server/server.js", "lint": "eslint --cache --fix", "format:types": "prettier --write \"types/**/*.{d.ts,ts,json}\"", "test": "pnpm --filter quasar-ui-test test", "test:watch": "pnpm --filter quasar-ui-test test:watch", "test:watch:ui": "pnpm --filter quasar-ui-test test:watch:ui", "test:specs": "node ./testing/specs/script.js", "test:specs:ci": "node ./testing/specs/script.js --ci"}}