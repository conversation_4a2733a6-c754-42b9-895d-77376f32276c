# 應用程式設定
app:
  name: "QS API"
  version: "1.0.0"
  port: 8082
  mode: "debug" # debug, release

# 主資料庫設定 (MySQL)
database:
  host: "localhost"
  port: "3306"
  user: "root"
  password: "forwork0926"
  name: "cy"

# 日誌設定
log:
  level: "info" # debug, info, warn, error
  format: "json" # json, text

# CORS 設定
cors:
  allowed_origins:
    - "http://localhost:3000"
    - "http://localhost:8081"
  allowed_methods:
    - "GET"
    - "POST"
    - "PUT"
    - "DELETE"
    - "OPTIONS"
  allowed_headers:
    - "Origin"
    - "Content-Type"
    - "Accept"
    - "Authorization"
    - "X-Requested-With"
