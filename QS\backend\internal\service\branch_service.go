package service

import (
	"qs/internal/dto"
	"qs/internal/repository"
)

type BranchService interface {
	GetAllBranches() ([]dto.BranchResponse, error)
	GetBranchSchedules(branchID uint) (*dto.BranchScheduleResponse, error)
}

type branchService struct {
	branchRepo repository.BranchRepository
}

func NewBranchService(branchRepo repository.BranchRepository) BranchService {
	return &branchService{
		branchRepo: branchRepo,
	}
}

func (s *branchService) GetAllBranches() ([]dto.BranchResponse, error) {
	branches, err := s.branchRepo.GetAll()
	if err != nil {
		return nil, err
	}

	var response []dto.BranchResponse
	for _, branch := range branches {
		response = append(response, dto.BranchResponse{
			ID:   branch.ID,
			Name: branch.Name,
		})
	}

	return response, nil
}

func (s *branchService) GetBranchSchedules(branchID uint) (*dto.BranchScheduleResponse, error) {
	// 首先檢查分店是否存在
	branch, err := s.branchRepo.GetByID(branchID)
	if err != nil {
		return nil, err
	}

	// TODO: 實作從 timing 資料庫獲取班次資訊的邏輯
	// 這裡需要根據分店的 timing 資料庫連線資訊來獲取實際的班次資料
	// 暫時返回模擬資料

	// 模擬資料 - 實際實作時需要從 timing 資料庫獲取
	mockSchedules := []dto.DoctorScheduleResponse{
		{
			ID:            1,
			DoctorName:    "王醫師",
			Shift:         "早班",
			CurrentNumber: 15,
			WaitingCount:  3,
		},
		{
			ID:            2,
			DoctorName:    "李醫師",
			Shift:         "午班",
			CurrentNumber: 8,
			WaitingCount:  5,
		},
	}

	response := &dto.BranchScheduleResponse{
		BranchID:   branch.ID,
		BranchName: branch.Name,
		Schedules:  mockSchedules,
	}

	return response, nil
}
