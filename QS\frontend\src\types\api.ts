// API 相關類型定義

// 統一的 API 回應格式
export interface APIResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
  error?: string;
}

// 分店相關類型
export interface Branch {
  id: number;
  name: string;
  address?: string;
  phone?: string;
}

// 醫師班次相關類型
export interface DoctorSchedule {
  id: number;
  doctor_name: string;
  shift: string;
  current_number: number;
  waiting_count: number;
}

// 分店班次回應
export interface BranchScheduleResponse {
  branch_id: number;
  branch_name: string;
  schedules: DoctorSchedule[];
}

// 選項類型（用於 q-select）
export interface SelectOption<T = any> {
  label: string;
  value: T;
  [key: string]: any;
}

// 錯誤類型
export interface APIError {
  message: string;
  status?: number;
  code?: string;
}
