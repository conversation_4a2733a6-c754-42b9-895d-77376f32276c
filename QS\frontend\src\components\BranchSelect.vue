<template>
  <div class="branch-select-container">
    <q-select
      v-model="selectedBranch"
      :options="branchOptions"
      :loading="loading"
      :disable="loading"
      emit-value
      map-options
      option-value="value"
      option-label="label"
      outlined
      class="q-mb-md white-background"
      @update:model-value="onBranchChange"
    >
      <template v-slot:no-option>
        <q-item>
          <q-item-section class="text-grey">
            {{ error ? '載入分店失敗' : '沒有可用的分店' }}
          </q-item-section>
        </q-item>
      </template>

      <template v-slot:loading>
        <q-item>
          <q-item-section>
            <q-item-label>載入中...</q-item-label>
          </q-item-section>
        </q-item>
      </template>
    </q-select>

    <!-- 錯誤提示 -->
    <q-banner
      v-if="error"
      class="text-white bg-negative q-mb-md"
      rounded
    >
      <template v-slot:avatar>
        <q-icon name="error" />
      </template>
      {{ error }}
      <template v-slot:action>
        <q-btn
          flat
          color="white"
          label="重試"
          @click="retryFetch"
        />
      </template>
    </q-banner>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue';
import { useBranches } from '@/composables/useBranches';
import type { Branch } from '@/types/api';

// Props
interface Props {
  modelValue?: number | null;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: null
});

// Emits
interface Emits {
  (e: 'update:modelValue', value: number | null): void;
  (e: 'branchChange', branch: Branch | null): void;
}

const emit = defineEmits<Emits>();

// 使用分店 composable
const {
  branchOptions,
  loading,
  error,
  fetchBranches,
  getBranchById
} = useBranches();

// 本地狀態
const selectedBranch = ref<number | null>(props.modelValue);

// 監聽 props 變化
watch(() => props.modelValue, (newValue) => {
  selectedBranch.value = newValue;
});

// 監聽選中分店變化
watch(selectedBranch, (newValue) => {
  emit('update:modelValue', newValue);
  const branchInfo = newValue ? getBranchById(newValue) : null;
  emit('branchChange', branchInfo);
});

// 分店變更處理
const onBranchChange = (value: number | null) => {
  selectedBranch.value = value;
};

// 重試獲取分店
const retryFetch = async () => {
  try {
    await fetchBranches();

    initializeSelection();
  } catch (error) {
    console.error('重試獲取分店失敗:', error);
  }
};

// 初始化選項
const initializeSelection = () => {
  // 預設選取第一個分店
  selectedBranch.value =
    branchOptions.value.length > 0
      ? branchOptions.value[0].value
      : null;
  emit('update:modelValue', selectedBranch.value);
  emit('branchChange', selectedBranch.value ? getBranchById(selectedBranch.value) : null);
};

// 組件掛載時獲取分店列表
onMounted(async () => {
  try {
    await fetchBranches();

    initializeSelection();
  } catch (error) {
    console.error('初始化獲取分店失敗:', error);
  }
});
</script>

<style scoped>
.branch-select-container {
  width: 100%;
  max-width: 400px;
  margin: 0 auto;
}

/* 白色背景的 select */
:deep(.white-background .q-field__control) {
  background-color: #ffffff !important; /* 純白色背景 */
  border: 1px solid #e0e0e0; /* 淺灰色邊框 */
  border-radius: 8px; /* 圓角 */

  font-size: 1.2rem;
}

/* 當 select 獲得焦點時的樣式 */
:deep(.white-background .q-field--focused .q-field__control) {
  background-color: #ffffff !important;
  border-color: #1976d2; /* Quasar 主色調 */
  box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.2);
}

/* 下拉箭頭顏色 */
:deep(.white-background .q-field__append .q-icon) {
  color: #666666;
}

/* 選項列表背景 */
:deep(.q-menu) {
  background-color: #ffffff;
  border: 1px solid #e0e0e0;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 選項項目樣式 */
:deep(.q-item) {
  background-color: #ffffff;
}

:deep(.q-item:hover) {
  background-color: #f5f5f5;
}

/* 選中的選項樣式 */
:deep(.q-item--active) {
  background-color: #e3f2fd !important;
  color: #1976d2;
}

/* 載入狀態文字顏色 */
:deep(.q-field__native) {
  color: #333333;
}

/* placeholder 文字顏色 */
:deep(.q-field__label) {
  color: #757575;
}
</style>
