<template>
  <div class="branch-select-container">
    <q-select
      v-model="selectedBranch"
      :options="branchOptions"
      :loading="loading"
      :disable="loading"
      label="選擇分店"
      emit-value
      map-options
      option-value="value"
      option-label="label"
      outlined
      class="q-mb-md"
      @update:model-value="onBranchChange"
    >
      <template v-slot:no-option>
        <q-item>
          <q-item-section class="text-grey">
            {{ error ? '載入分店失敗' : '沒有可用的分店' }}
          </q-item-section>
        </q-item>
      </template>

      <template v-slot:loading>
        <q-item>
          <q-item-section>
            <q-item-label>載入中...</q-item-label>
          </q-item-section>
        </q-item>
      </template>
    </q-select>

    <!-- 錯誤提示 -->
    <q-banner
      v-if="error"
      class="text-white bg-negative q-mb-md"
      rounded
    >
      <template v-slot:avatar>
        <q-icon name="error" />
      </template>
      {{ error }}
      <template v-slot:action>
        <q-btn
          flat
          color="white"
          label="重試"
          @click="retryFetch"
        />
      </template>
    </q-banner>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue';
import { useBranches } from '@/composables/useBranches';
import type { Branch } from '@/types/api';

// Props
interface Props {
  modelValue?: number | null;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: null
});

// Emits
interface Emits {
  (e: 'update:modelValue', value: number | null): void;
  (e: 'branchChange', branch: Branch | null): void;
}

const emit = defineEmits<Emits>();

// 使用分店 composable
const {
  branchOptions,
  loading,
  error,
  fetchBranches,
  getBranchById
} = useBranches();

// 本地狀態
const selectedBranch = ref<number | null>(props.modelValue);

// 監聽 props 變化
watch(() => props.modelValue, (newValue) => {
  selectedBranch.value = newValue;
});

// 監聽選中分店變化
watch(selectedBranch, (newValue) => {
  emit('update:modelValue', newValue);
  const branchInfo = newValue ? getBranchById(newValue) : null;
  emit('branchChange', branchInfo);
});

// 分店變更處理
const onBranchChange = (value: number | null) => {
  selectedBranch.value = value;
};

// 重試獲取分店
const retryFetch = async () => {
  try {
    await fetchBranches();

    initializeSelection();
  } catch (error) {
    console.error('重試獲取分店失敗:', error);
  }
};

// 初始化選項
const initializeSelection = () => {
  // 預設選取第一個分店
  selectedBranch.value =
    branchOptions.value.length > 0
      ? branchOptions.value[0].value
      : null;
  emit('update:modelValue', selectedBranch.value);
  emit('branchChange', selectedBranch.value ? getBranchById(selectedBranch.value) : null);
};

// 組件掛載時獲取分店列表
onMounted(async () => {
  try {
    await fetchBranches();

    initializeSelection();
  } catch (error) {
    console.error('初始化獲取分店失敗:', error);
  }
});
</script>

<style scoped>
.branch-select-container {
  width: 100%;
  max-width: 400px;
  margin: 0 auto;
}
</style>
