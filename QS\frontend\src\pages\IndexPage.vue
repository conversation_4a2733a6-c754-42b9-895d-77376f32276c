<template>
  <q-page class="q-py-lg q-px-xs">
    <div class="row">
      <div class="col col-12">
        <!-- 標題 -->
        <div class="text-center">
          <h4 class="text-h4 q-my-md">診所看診進度查詢</h4>
          <div class="flex items-center justify-between">
            <div class="hint-text">＊實際掛號進度以現場為主＊</div>
            <q-btn
              round
              color="primary"
              icon="refresh"
              size="md"
              :loading="refreshLoading"
              @click="handleRefresh"
              class="refresh-btn"
            >
              <q-tooltip>重新整理掛號進度</q-tooltip>
            </q-btn>
          </div>
        </div>
      </div>
    </div>

    <div class="row q-mb-lg">
      <div class="col col-12">
          <BranchSelect
            ref="branchSelectRef"
            v-model="selectedBranchId"
            @branch-change="onBranchChange"
          />
      </div>
    </div>
    <div class="row">
      <div class="col col-12">
        <!-- 未來可以在這裡添加醫師班次卡片 -->
        <div v-if="selectedBranchId" class="text-center text-grey-6">
          <q-icon name="schedule" size="3em" class="q-mb-md" />
          <p>醫師班次資訊即將推出...</p>
        </div>

      </div>
    </div>
  </q-page>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { Notify } from 'quasar';
import BranchSelect from 'components/BranchSelect.vue';
import type { Branch } from '@/types/api';

// 響應式狀態
const selectedBranchId = ref<number | null>(null);
const selectedBranch = ref<Branch | null>(null);
const refreshLoading = ref(false);
const branchSelectRef = ref();

// 分店變更處理
const onBranchChange = (branch: Branch | null) => {
  selectedBranch.value = branch;
  console.log('選中的分店:', branch);
};

// 手動重新整理處理
const handleRefresh = async () => {
  refreshLoading.value = true;

  try {
    // 如果有 BranchSelect 組件的引用，調用其重新獲取方法
    if (branchSelectRef.value && typeof branchSelectRef.value.retryFetch === 'function') {
      await branchSelectRef.value.retryFetch();
    }

    // 顯示成功通知
    Notify.create({
      type: 'positive',
      message: '掛號進度已更新',
      position: 'top',
      timeout: 2000
    });

    console.log('手動重新整理完成');
  } catch (error) {
    console.error('重新整理失敗:', error);

    // 顯示錯誤通知
    Notify.create({
      type: 'negative',
      message: '更新失敗，請稍後再試',
      position: 'top',
      timeout: 3000
    });
  } finally {
    refreshLoading.value = false;
  }
};
</script>

<style scoped>
.q-page {
  width: 100%;
  max-width: 600px;
  margin: 0 auto;
}

.hint-text {
  color: #207daf;
  font-size: 1rem;
  font-weight: bold;
}

.refresh-btn {
  min-width: 48px;
  min-height: 48px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.refresh-btn:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.refresh-btn:active {
  transform: scale(0.95);
}

/* 確保 flex 容器在小螢幕上也能正常顯示 */
@media (max-width: 600px) {
  .flex {
    flex-direction: column;
    align-items: stretch;
  }

  .refresh-btn {
    align-self: center;
    margin-top: 12px;
  }
}
</style>
