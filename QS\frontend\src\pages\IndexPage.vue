<template>
  <q-page class="q-py-lg q-px-xs">
    <div class="row">
      <div class="col col-12">
        <!-- 標題 -->
        <div class="text-center">
          <h4 class="text-h4 q-my-md">診所看診進度查詢</h4>
          <div class="flex items-center justify-between">
            <div class="hint-text">＊實際掛號進度以現場為主＊</div>
            <div class="refresh-container">
              <q-btn
                round
                :color="coolDownRemaining > 0 ? 'grey-5' : 'primary'"
                :icon="coolDownRemaining > 0 ? 'timer' : 'refresh'"
                size="md"
                :loading="refreshLoading"
                :disable="refreshLoading || coolDownRemaining > 0"
                @click="handleRefresh"
                class="refresh-btn"
              >
                <q-tooltip>
                  {{ coolDownRemaining > 0 ? `請等待 ${coolDownRemaining} 秒` : '重新整理掛號進度' }}
                </q-tooltip>
              </q-btn>
              <div v-if="coolDownRemaining > 0" class="cooldown-text">
                {{ coolDownRemaining }}s
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="row q-mb-lg">
      <div class="col col-12">
          <BranchSelect
            ref="branchSelectRef"
            v-model="selectedBranchId"
            @branch-change="onBranchChange"
          />
      </div>
    </div>
    <div class="row">
      <div class="col col-12">
        <!-- 班次資訊顯示 -->
        <div v-if="selectedBranchId">
          <!-- 載入狀態 -->
          <div v-if="schedulesLoading" class="text-center q-py-lg">
            <q-spinner-dots size="2em" color="primary" />
            <p class="q-mt-md text-grey-6">載入班次資訊中...</p>
          </div>

          <!-- 錯誤狀態 -->
          <div v-else-if="schedulesError" class="text-center q-py-lg">
            <q-icon name="error" size="3em" color="negative" class="q-mb-md" />
            <p class="text-negative">{{ schedulesError }}</p>
          </div>

          <!-- 班次資訊 -->
          <div v-else-if="schedules" class="q-py-md">
            <h5 class="text-h5 q-mb-md text-center">{{ schedules.branch_name }} - 看診進度</h5>

            <div class="row q-gutter-md">
              <div
                v-for="schedule in schedules.schedules"
                :key="schedule.id"
                class="col-12 col-md-6"
              >
                <q-card class="schedule-card">
                  <q-card-section>
                    <div class="row items-center">
                      <div class="col">
                        <div class="text-h6">{{ schedule.doctor_name }}</div>
                        <div class="text-subtitle2 text-grey-6">{{ schedule.shift }}</div>
                      </div>
                      <div class="col-auto">
                        <q-icon name="schedule" size="2em" color="primary" />
                      </div>
                    </div>
                  </q-card-section>

                  <q-card-section class="q-pt-none">
                    <div class="row q-gutter-md">
                      <div class="col">
                        <div class="text-center">
                          <div class="text-h4 text-primary">{{ schedule.current_number }}</div>
                          <div class="text-caption text-grey-6">目前看診號</div>
                        </div>
                      </div>
                      <div class="col">
                        <div class="text-center">
                          <div class="text-h4 text-orange">{{ schedule.waiting_count }}</div>
                          <div class="text-caption text-grey-6">等待人數</div>
                        </div>
                      </div>
                    </div>
                  </q-card-section>
                </q-card>
              </div>
            </div>
          </div>

          <!-- 無班次資訊 -->
          <div v-else class="text-center text-grey-6 q-py-lg">
            <q-icon name="schedule" size="3em" class="q-mb-md" />
            <p>暫無班次資訊</p>
          </div>
        </div>
      </div>
    </div>
  </q-page>
</template>

<script setup lang="ts">
import { ref, onUnmounted } from 'vue';
import { Notify } from 'quasar';
import BranchSelect from 'components/BranchSelect.vue';
import { useSchedules } from '@/composables/useSchedules';
import type { Branch } from '@/types/api';

// 響應式狀態
const selectedBranchId = ref<number | null>(null);
const selectedBranch = ref<Branch | null>(null);
const refreshLoading = ref(false);
const branchSelectRef = ref();
const coolDownRemaining = ref(0);

// 冷卻時間設定（秒）
const COOL_DOWN_DURATION = 30;
let coolDownTimer: NodeJS.Timeout | null = null;

// 使用掛號進度 composable
const {
  schedules,
  loading: schedulesLoading,
  error: schedulesError,
  fetchBranchSchedules
} = useSchedules();

// 分店變更處理
const onBranchChange = async (branch: Branch | null) => {
  selectedBranch.value = branch;

  await fetchQueueProgress();
};

// 開始冷卻倒數
const startCoolDown = () => {
  coolDownRemaining.value = COOL_DOWN_DURATION;

  coolDownTimer = setInterval(() => {
    coolDownRemaining.value--;

    if (coolDownRemaining.value <= 0) {
      clearInterval(coolDownTimer);
      coolDownTimer = null;
    }
  }, 1000);
};

// 手動重新整理處理
const handleRefresh = async () => {
  // 如果在冷卻期間，直接返回
  if (coolDownRemaining.value > 0) {
    return;
  }

  refreshLoading.value = true;

  try {
    // 獲得掛號進度
    await fetchQueueProgress();

    // 顯示成功通知
    Notify.create({
      type: 'positive',
      message: '掛號進度已更新',
      position: 'top',
      timeout: 2000
    });

    // 開始冷卻時間
    startCoolDown();
  } catch (error) {
    console.error('重新整理失敗:', error);

    // 顯示錯誤通知
    Notify.create({
      type: 'negative',
      message: '更新失敗，請稍後再試',
      position: 'top',
      timeout: 3000
    });
  } finally {
    refreshLoading.value = false;
  }
};

// 獲得掛號進度
const fetchQueueProgress = async () => {
  if (!selectedBranchId.value) return;

  try {
    // 調用掛號進度 API
    await fetchBranchSchedules(selectedBranchId.value);
  } catch (error) {
    console.error('獲取掛號進度失敗:', error);
    throw error; // 重新拋出錯誤，讓上層處理
  }
};

// 組件卸載時清理計時器
onUnmounted(() => {
  if (coolDownTimer) {
    clearInterval(coolDownTimer);
    coolDownTimer = null;
  }
});
</script>

<style scoped>
.q-page {
  width: 100%;
  max-width: 600px;
  margin: 0 auto;
}

.hint-text {
  color: #207daf;
  font-size: 1rem;
  font-weight: bold;
}

.refresh-container {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.refresh-btn {
  min-width: 48px;
  min-height: 48px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.refresh-btn:hover:not(:disabled) {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.refresh-btn:active:not(:disabled) {
  transform: scale(0.95);
}

.refresh-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.cooldown-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 12px;
  font-weight: bold;
  color: #666;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  pointer-events: none;
}

/* 班次卡片樣式 */
.schedule-card {
  border-left: 4px solid #1976d2;
  transition: all 0.3s ease;
}

.schedule-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

/* 確保 flex 容器在小螢幕上也能正常顯示 */
@media (max-width: 600px) {
  .flex {
    flex-direction: column;
    align-items: stretch;
  }

  .refresh-container {
    align-self: center;
    margin-top: 12px;
  }

  .schedule-card {
    margin-bottom: 16px;
  }
}
</style>
