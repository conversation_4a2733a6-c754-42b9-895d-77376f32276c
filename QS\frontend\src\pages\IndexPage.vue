<template>
  <q-page class="q-pa-lg">
    <div class="row justify-center">
      <div class="col-12 col-md-8 col-lg-6">
        <!-- 標題 -->
        <div class="text-center q-mb-lg">
          <h4 class="text-h4 q-my-md">診所看診進度查詢</h4>
          <p class="text-body1 text-grey-7">＊實際掛號看診進度以現場為主＊</p>
        </div>

        <!-- 分店選擇 -->
        <div class="q-mb-lg">
          <BranchSelect
            v-model="selectedBranchId"
            @branch-change="onBranchChange"
          />
        </div>

        <!-- 未來可以在這裡添加醫師班次卡片 -->
        <div v-if="selectedBranchId" class="text-center text-grey-6">
          <q-icon name="schedule" size="3em" class="q-mb-md" />
          <p>醫師班次資訊即將推出...</p>
        </div>
      </div>
    </div>
  </q-page>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import BranchSelect from 'components/BranchSelect.vue';
import type { Branch } from '@/types/api';

// 響應式狀態
const selectedBranchId = ref<number | null>(null);
const selectedBranch = ref<Branch | null>(null);

// 分店變更處理
const onBranchChange = (branch: Branch | null) => {
  selectedBranch.value = branch;
  console.log('選中的分店:', branch);
};
</script>
