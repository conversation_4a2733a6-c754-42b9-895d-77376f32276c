import { ref } from 'vue';
import { api } from '@/boot/axios';
import type { BranchScheduleResponse, APIResponse } from '@/types/api';

// 掛號進度 composable
export function useSchedules() {
  // 響應式狀態
  const schedules = ref<BranchScheduleResponse | null>(null);
  const loading = ref(false);
  const error = ref<string | null>(null);

  // 獲取指定分店的班次資訊
  const fetchBranchSchedules = async (branchId: number) => {
    if (!branchId) {
      error.value = '請選擇分店';
      return;
    }

    loading.value = true;
    error.value = null;
    
    try {
      const response = await api.get<APIResponse<BranchScheduleResponse>>(
        `/branches/${branchId}/schedules`
      );
      
      if (response.data.success && response.data.data) {
        schedules.value = response.data.data;
      } else {
        throw new Error(response.data.message || '獲取班次資訊失敗');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '網路錯誤';
      error.value = errorMessage;
      console.error('獲取班次資訊失敗:', err);
      
      // 清空之前的資料
      schedules.value = null;
    } finally {
      loading.value = false;
    }
  };

  // 重置狀態
  const reset = () => {
    schedules.value = null;
    error.value = null;
    loading.value = false;
  };

  return {
    // 狀態
    schedules,
    loading,
    error,
    
    // 方法
    fetchBranchSchedules,
    reset
  };
}
