import "./chunk-PZ5AY32C.js";

// node_modules/@quasar/app-vite/exports/wrappers/wrappers.js
var wrapper = (callback) => callback;
var defineConfig = wrapper;
var defineBoot = wrapper;
var definePreFetch = wrapper;
var defineRouter = wrapper;
var defineStore = wrapper;
var defineSsrMiddleware = wrapper;
var defineSsrCreate = wrapper;
var defineSsrInjectDevMiddleware = wrapper;
var defineSsrListen = wrapper;
var defineSsrClose = wrapper;
var defineSsrServeStaticContent = wrapper;
var defineSsrRenderPreloadTag = wrapper;
export {
  defineBoot,
  defineConfig,
  definePreFetch,
  defineRouter,
  defineSsrClose,
  defineSsrCreate,
  defineSsrInjectDevMiddleware,
  defineSsrListen,
  defineSsrMiddleware,
  defineSsrRenderPreloadTag,
  defineSsrServeStaticContent,
  defineStore
};
//# sourceMappingURL=@quasar_app-vite_wrappers.js.map
