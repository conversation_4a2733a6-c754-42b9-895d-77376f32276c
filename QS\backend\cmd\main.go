package main

import (
	"context"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"qs/internal/api"
	"qs/internal/db"
	"qs/internal/repository"
	"qs/internal/service"

	"github.com/gin-gonic/gin"
	"github.com/spf13/viper"
	"gorm.io/gorm"
)

func main() {
	// 初始化配置
	initConfig()

	// 初始化資料庫
	database := initDatabase()

	// 初始化服務
	branchRepo := repository.NewBranchRepository(database)
	branchService := service.NewBranchService(branchRepo)

	// 初始化 Gin 引擎
	router := initGin()

	// 註冊路由
	registerRoutes(router, branchService)

	// 啟動優雅伺服器
	startServer(router)
}

// initConfig 初始化配置
func initConfig() {
	viper.SetConfigName("config")
	viper.SetConfigType("yaml")
	viper.AddConfigPath(".")
	viper.AddConfigPath("./config")

	// 自動讀取環境變數
	viper.AutomaticEnv()

	// 讀取配置檔案
	if err := viper.ReadInConfig(); err != nil {
		log.Printf("Warning: Config file not found, using defaults and environment variables: %v", err)
	}

	log.Printf("Using config file: %s", viper.ConfigFileUsed())
}

// initDatabase 初始化資料庫連線
func initDatabase() *gorm.DB {
	log.Println("Connecting to main database...")
	database := db.ConnectDB()
	log.Println("Main database connected successfully")
	return database
}

// initGin 初始化 Gin 引擎
func initGin() *gin.Engine {
	// 設定 Gin 模式
	gin.SetMode(viper.GetString("app.mode"))

	router := gin.New()

	// 中介軟體
	router.Use(gin.Logger())
	router.Use(gin.Recovery())
	router.Use(corsMiddleware())

	return router
}

// corsMiddleware CORS 中介軟體
func corsMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Header("Access-Control-Allow-Origin", "*")
		c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Origin, Content-Type, Accept, Authorization, X-Requested-With")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}

		c.Next()
	}
}

// registerRoutes 註冊路由
func registerRoutes(router *gin.Engine, branchService service.BranchService) {
	// 健康檢查
	healthHandler := api.NewHealthHandler()
	healthHandler.RegisterRoutes(router)

	// API v1 路由群組
	v1 := router.Group("/api/v1")
	{
		// 分店相關路由
		branchHandler := api.NewBranchHandler(branchService)
		branchHandler.RegisterRoutes(v1)
	}

	// 列出所有路由（開發模式）
	if viper.GetString("app.mode") == "debug" {
		log.Println("Registered routes:")
		for _, route := range router.Routes() {
			log.Printf("  %s %s", route.Method, route.Path)
		}
	}
}

// startServer 啟動優雅伺服器
func startServer(router *gin.Engine) {
	port := viper.GetString("app.port")
	if port == "" {
		port = "8080"
	}

	srv := &http.Server{
		Addr:    ":" + port,
		Handler: router,
	}

	// 在 goroutine 中啟動伺服器
	go func() {
		log.Printf("Starting server on port %s", port)
		log.Printf("Server running at http://localhost:%s", port)

		if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatalf("Failed to start server: %v", err)
		}
	}()

	// 等待中斷信號以優雅地關閉伺服器
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit
	log.Println("Shutting down server...")

	// 5 秒的超時時間來完成現有請求
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := srv.Shutdown(ctx); err != nil {
		log.Fatalf("Server forced to shutdown: %v", err)
	}

	log.Println("Server exited")
}
