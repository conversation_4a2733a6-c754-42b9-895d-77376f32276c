import { ref, computed } from 'vue';
import { api } from '@/boot/axios';
import type { Branch, APIResponse, SelectOption } from '@/types/api';

// 分店 composable
export function useBranches() {
  // 響應式狀態
  const branches = ref<Branch[]>([]);
  const loading = ref(false);
  const error = ref<string | null>(null);

  // 計算屬性：格式化為 q-select 需要的選項格式
  const branchOptions = computed((): SelectOption<number>[] =>
    branches.value.map(branch => ({
      label: branch.name,
      value: branch.id,
      ...branch
    }))
  );

  // 獲取所有分店
  const fetchBranches = async () => {
    loading.value = true;
    error.value = null;

    try {
      const response = await api.get<APIResponse<Branch[]>>('/branches');

      if (response.data.success && response.data.data) {
        branches.value = response.data.data;
      } else {
        throw new Error(response.data.message || '獲取分店列表失敗');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '網路錯誤';
      error.value = errorMessage;
      console.error('獲取分店列表失敗:', err);

      // 可以在這裡添加錯誤通知
      // 例如使用 Quasar 的 Notify
    } finally {
      loading.value = false;
    }
  };

  // 根據 ID 獲取分店
  const getBranchById = (id: number): Branch | undefined => {
    return branches.value.find(branch => branch.id === id);
  };

  // 重置狀態
  const reset = () => {
    branches.value = [];
    error.value = null;
    loading.value = false;
  };

  return {
    // 狀態
    branches,
    branchOptions,
    loading,
    error,

    // 方法
    fetchBranches,
    getBranchById,
    reset
  };
}
