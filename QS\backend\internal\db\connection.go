package db

import (
	"fmt"
	"log"
	"os"
	"time"

	"github.com/spf13/viper"
	"gorm.io/driver/mysql"
	"gorm.io/driver/sqlserver"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// ConnectDB connects to the main database
func ConnectDB() *gorm.DB {
	// 嘗試從環境變數讀取，如果沒有則從配置檔案讀取
	dbUser := viper.GetString("DB_USER")
	if dbUser == "" {
		dbUser = viper.GetString("database.user")
	}

	dbPassword := viper.GetString("DB_PASSWORD")
	if dbPassword == "" {
		dbPassword = viper.GetString("database.password")
	}

	dbHost := viper.GetString("DB_HOST")
	if dbHost == "" {
		dbHost = viper.GetString("database.host")
	}

	dbPort := viper.GetString("DB_PORT")
	if dbPort == "" {
		dbPort = viper.GetString("database.port")
	}

	dbName := viper.GetString("DB_NAME")
	if dbName == "" {
		dbName = viper.GetString("database.name")
	}

	// 驗證必要的配置參數
	if dbUser == "" || dbPassword == "" || dbHost == "" || dbPort == "" || dbName == "" {
		log.Fatalf("Missing required database configuration. Please check your config file or environment variables.\nRequired: DB_USER, DB_PASSWORD, DB_HOST, DB_PORT, DB_NAME")
	}

	dsn := fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?parseTime=True&loc=Local&charset=utf8mb4&collation=utf8mb4_unicode_ci",
		dbUser,
		dbPassword,
		dbHost,
		dbPort,
		dbName,
	)

	log.Printf("Connecting to database: %s@%s:%s/%s", dbUser, dbHost, dbPort, dbName)

	dbLogger := getDBLogger()

	conn, err := gorm.Open(mysql.Open(dsn), &gorm.Config{
		Logger: dbLogger,
	})

	if err != nil {
		log.Fatalf("Failed to connect to main database: %v", err)
	}

	// 設定連線池
	sqlDB, err := conn.DB()
	if err != nil {
		log.Fatalf("Failed to get underlying sql.DB: %v", err)
	}

	// 設定連線池參數
	sqlDB.SetMaxIdleConns(10)           // 最大閒置連線數
	sqlDB.SetMaxOpenConns(100)          // 最大開啟連線數
	sqlDB.SetConnMaxLifetime(time.Hour) // 連線最大生命週期

	return conn
}

// ConnectTimingDB connects to the timing database
func ConnectTimingDB(config *DBConfig) (*gorm.DB, error) {
	dsn := fmt.Sprintf("sqlserver://%s:%s@%s:%s?database=%s&encrypt=disable",
		config.User,
		config.Password,
		config.Host,
		config.Port,
		config.Name,
	)

	dbLogger := getDBLogger()

	conn, err := gorm.Open(sqlserver.Open(dsn), &gorm.Config{
		Logger: dbLogger,
	})

	if err != nil {
		return nil, fmt.Errorf("failed to connect to timing database: %w", err)
	}

	// 設定連線池（較小的連線池，因為是臨時連線）
	sqlDB, err := conn.DB()
	if err != nil {
		return nil, fmt.Errorf("failed to get underlying sql.DB: %w", err)
	}

	// 設定連線池參數（較保守的設定）
	sqlDB.SetMaxIdleConns(2)                   // 最大閒置連線數
	sqlDB.SetMaxOpenConns(10)                  // 最大開啟連線數
	sqlDB.SetConnMaxLifetime(30 * time.Minute) // 連線最大生命週期

	return conn, nil
}

// getDBLogger 取得資料庫日誌設定
func getDBLogger() logger.Interface {
	logLevel := logger.Info
	if viper.GetString("GIN_MODE") == "release" {
		logLevel = logger.Error
	}

	return logger.New(
		log.New(os.Stdout, "\r\n", log.LstdFlags),
		logger.Config{
			SlowThreshold: time.Second,
			LogLevel:      logLevel,
			Colorful:      true,
		},
	)
}
