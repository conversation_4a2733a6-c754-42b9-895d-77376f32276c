# 前端 API 整合說明

## 概述

本文件說明前端如何與後端 API 整合，特別是 BranchSelect 組件的 API 功能實現。

## 架構設計

### 1. API 配置 (`src/boot/axios.ts`)

- 配置 axios 實例
- 設定基礎 URL 和請求/回應攔截器
- 支援開發和生產環境的不同配置

### 2. 類型定義 (`src/types/api.ts`)

- 定義 API 回應格式
- 定義分店、醫師班次等業務實體類型
- 提供 TypeScript 類型安全

### 3. Composable (`src/composables/useBranches.ts`)

- 封裝分店相關的 API 調用邏輯
- 提供響應式狀態管理
- 處理載入狀態和錯誤處理

### 4. 組件 (`src/components/BranchSelect.vue`)

- 分店選擇下拉選單
- 整合 API 功能
- 提供用戶友好的介面

## API 端點

### 獲取所有分店

**端點:** `GET /api/v1/branches`

**回應格式:**
```json
{
  "success": true,
  "message": "取得分店列表成功",
  "data": [
    {
      "id": 1,
      "name": "北門診所",
      "address": "台北市中正區北門路100號",
      "phone": "02-1234-5678"
    }
  ]
}
```

## 使用方式

### 1. 在組件中使用 BranchSelect

```vue
<template>
  <BranchSelect 
    v-model="selectedBranchId"
    @branch-change="onBranchChange"
  />
</template>

<script setup lang="ts">
import { ref } from 'vue';
import BranchSelect from 'components/BranchSelect.vue';
import type { Branch } from 'src/types/api';

const selectedBranchId = ref<number | null>(null);

const onBranchChange = (branch: Branch | null) => {
  console.log('選中的分店:', branch);
};
</script>
```

### 2. 直接使用 composable

```typescript
import { useBranches } from 'src/composables/useBranches';

const { 
  branches, 
  branchOptions, 
  loading, 
  error, 
  fetchBranches 
} = useBranches();

// 獲取分店列表
await fetchBranches();
```

## 環境配置

### 開發環境 (`.env.dev`)

- 使用 proxy 設定，API 請求會被代理到後端服務
- 後端服務運行在 `http://localhost:8082`

### 生產環境 (`.env.prod`)

- 使用相對路徑，需要配置 nginx 或其他反向代理

## 錯誤處理

1. **網路錯誤**: 顯示重試按鈕
2. **API 錯誤**: 顯示錯誤訊息
3. **載入狀態**: 顯示載入指示器

## 開發注意事項

1. 確保後端服務正在運行 (`http://localhost:8082`)
2. 檢查 CORS 設定是否正確
3. 確認 proxy 設定指向正確的後端端口
4. 使用 TypeScript 確保類型安全

## 測試

1. 啟動後端服務
2. 啟動前端開發服務器
3. 檢查瀏覽器開發者工具的網路標籤
4. 驗證 API 請求是否成功

## 擴展功能

未來可以添加：

1. 醫師班次查詢 API
2. 即時更新功能 (WebSocket)
3. 錯誤重試機制
4. 快取機制
5. 離線支援
