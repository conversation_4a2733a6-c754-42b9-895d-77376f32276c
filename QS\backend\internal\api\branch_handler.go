package api

import (
	"net/http"
	"strconv"

	"qs/internal/dto"
	"qs/internal/service"

	"github.com/gin-gonic/gin"
)

type BranchHandler struct {
	branchService service.BranchService
}

func NewBranchHandler(branchService service.BranchService) *BranchHandler {
	return &BranchHandler{
		branchService: branchService,
	}
}

// GetBranches 取得所有分店
// @Summary 取得所有分店列表
// @Description 取得系統中所有分店的基本資訊
// @Tags branches
// @Accept json
// @Produce json
// @Success 200 {object} dto.APIResponse{data=[]dto.BranchResponse}
// @Failure 500 {object} dto.APIResponse
// @Router /api/v1/branches [get]
func (h *BranchHandler) GetBranches(c *gin.Context) {
	branches, err := h.branchService.GetAllBranches()
	if err != nil {
		c.JSON(http.StatusInternalServerError, dto.ErrorResponse("取得分店列表失敗", err))
		return
	}

	c.JSO<PERSON>(http.StatusOK, dto.SuccessResponse("取得分店列表成功", branches))
}

// GetBranchSchedules 取得指定分店的班次資訊
// @Summary 取得指定分店的班次資訊
// @Description 取得指定分店當前的醫師班次和掛號進度
// @Tags branches
// @Accept json
// @Produce json
// @Param id path int true "分店 ID"
// @Success 200 {object} dto.APIResponse{data=dto.BranchScheduleResponse}
// @Failure 400 {object} dto.APIResponse
// @Failure 404 {object} dto.APIResponse
// @Failure 500 {object} dto.APIResponse
// @Router /api/v1/branches/{id}/schedules [get]
func (h *BranchHandler) GetBranchSchedules(c *gin.Context) {
	// 解析分店 ID
	idParam := c.Param("id")
	branchID, err := strconv.ParseUint(idParam, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, dto.ErrorResponse("無效的分店 ID", err))
		return
	}

	// 調用服務層獲取班次資訊
	schedules, err := h.branchService.GetBranchSchedules(uint(branchID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, dto.ErrorResponse("取得班次資訊失敗", err))
		return
	}

	c.JSON(http.StatusOK, dto.SuccessResponse("取得班次資訊成功", schedules))
}

// RegisterRoutes 註冊路由
func (h *BranchHandler) RegisterRoutes(router *gin.RouterGroup) {
	branches := router.Group("/branches")
	{
		branches.GET("", h.GetBranches)
		branches.GET("/:id/schedules", h.GetBranchSchedules)
	}
}
